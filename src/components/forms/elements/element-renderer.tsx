import { type FormField } from "@/types/form.types";
import { getElement, hasElement } from "./registry";

interface ElementRendererProps {
  field: FormField;
  index?: number;
}

/**
 * Universal element renderer that uses the registry system.
 * This replaces the massive renderElement function with a clean, registry-based approach.
 */
export function ElementRenderer({ field, index }: ElementRendererProps) {
  // Check if the element type is registered in the new registry
  if (hasElement(field.subtype as any)) {
    const { Renderer } = getElement(field.subtype as any);
    return <Renderer field={field} index={index} />;
  }

  // Fallback for unregistered elements (during migration)
  return (
    <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
      <p className="text-red-600 text-sm">
        Element type "{field.subtype}" is not yet registered in the new system.
      </p>
      <p className="text-red-500 text-xs mt-1">
        Field ID: {field.id}
      </p>
    </div>
  );
}
