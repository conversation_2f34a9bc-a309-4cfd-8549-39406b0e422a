import type { ReactNode } from "react";
import type { FormField, FormElementSubType } from "@/types/form.types";

/**
 * Element registry system for form builder components.
 * This replaces the massive renderElement function with a clean, extensible registry pattern.
 */

export type ElementKind = FormElementSubType;

export interface ElementDefinition {
  kind: ElementKind;
  label: string;
  icon: ReactNode;
  category: "basic" | "choice" | "contact" | "advanced";
  createDefault: () => Partial<FormField>;
  Renderer: React.ComponentType<ElementRendererProps>;
}

export interface ElementRendererProps {
  field: FormField;
  index?: number;
}

// Internal registry storage
const registry = new Map<ElementKind, ElementDefinition>();

/**
 * Register a new element type in the registry.
 * @param definition - The element definition to register
 */
export const registerElement = (definition: ElementDefinition): void => {
  registry.set(definition.kind, definition);
};

/**
 * Get an element definition by its kind.
 * @param kind - The element kind to retrieve
 * @returns The element definition
 * @throws Error if element kind is not found
 */
export const getElement = (kind: ElementKind): ElementDefinition => {
  const element = registry.get(kind);
  if (!element) {
    throw new Error(`Element kind "${kind}" not found in registry`);
  }
  return element;
};

/**
 * Check if an element kind is registered.
 * @param kind - The element kind to check
 * @returns True if the element is registered
 */
export const hasElement = (kind: ElementKind): boolean => {
  return registry.has(kind);
};

/**
 * Get all registered element definitions.
 * @returns Array of all element definitions
 */
export const listElements = (): ElementDefinition[] => {
  return Array.from(registry.values());
};

/**
 * Get elements by category.
 * @param category - The category to filter by
 * @returns Array of element definitions in the specified category
 */
export const getElementsByCategory = (
  category: ElementDefinition["category"]
): ElementDefinition[] => {
  return listElements().filter((element) => element.category === category);
};

/**
 * Clear all registered elements (mainly for testing).
 */
export const clearRegistry = (): void => {
  registry.clear();
};

/**
 * Get the count of registered elements.
 * @returns Number of registered elements
 */
export const getRegistrySize = (): number => {
  return registry.size;
};
