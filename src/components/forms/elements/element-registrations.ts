import { IconHeading, IconAt } from "@tabler/icons-react";
import { CgDetailsLess } from "react-icons/cg";
import { nanoid } from "@/libs/nanoid";
import { registerElement, type ElementDefinition } from "./registry";
import { HeadingElement } from "./heading-element";
import { ShortAnswerElement } from "./short-answer-element";
import { EmailElement } from "./email-element";

/**
 * Register all available form elements.
 * This file contains the element definitions for the registry system.
 */

// Heading Element Registration
registerElement({
  kind: "heading",
  label: "Heading",
  icon: IconHeading({ size: 16 }),
  category: "basic",
  createDefault: () => ({
    id: nanoid(),
    label: "Section heading",
    subtype: "heading",
    type: "text",
    required: false,
    description: "",
    showDescription: false,
    options: [],
  }),
  Renderer: HeadingElement,
});

// Short Answer Element Registration
registerElement({
  kind: "short_answer",
  label: "Short Answer",
  icon: CgDetailsLess({ size: 16 }),
  category: "basic",
  createDefault: () => ({
    id: nanoid(),
    label: "Untitled Question",
    subtype: "short_answer",
    type: "text",
    required: false,
    description: "",
    showDescription: false,
    options: [],
  }),
  Renderer: ShortAnswerElement,
});

// Email Element Registration
registerElement({
  kind: "email",
  label: "Email",
  icon: IconAt({ size: 16 }),
  category: "contact",
  createDefault: () => ({
    id: nanoid(),
    label: "Email",
    subtype: "email",
    type: "email",
    required: true,
    description: "",
    showDescription: false,
    options: [],
  }),
  Renderer: EmailElement,
});

// Export for convenience
export { registerElement, getElement, hasElement, listElements, getElementsByCategory } from "./registry";
